#!/usr/bin/env python3
"""
配置测试脚本
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import settings
from loguru import logger


def test_database_config():
    """测试数据库配置"""
    logger.info("=== 数据库配置测试 ===")
    logger.info(f"数据库主机: {settings.db_host}")
    logger.info(f"数据库端口: {settings.db_port}")
    logger.info(f"数据库用户: {settings.db_user}")
    logger.info(f"数据库名称: {settings.db_name}")
    logger.info(f"数据库驱动: {settings.db_driver}")
    logger.info(f"完整连接URL: {settings.get_database_url}")
    
    # 测试数据库连接
    try:
        from app.core.database import engine
        with engine.connect() as conn:
            result = conn.execute("SELECT 1 as test")
            logger.info("✅ 数据库连接成功")
            return True
    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {str(e)}")
        return False


def test_ai_config():
    """测试AI配置"""
    logger.info("=== AI配置测试 ===")
    
    # 千问配置
    if settings.dashscope_api_key:
        logger.info(f"✅ 千问API密钥已配置: {settings.dashscope_api_key[:10]}...")
        logger.info(f"千问模型: {settings.qwen_model}")
        logger.info(f"千问API地址: {settings.qwen_base_url}")
        
        # 测试千问API
        try:
            from app.services.qwen_service import QwenService
            qwen_service = QwenService()
            logger.info("✅ 千问服务初始化成功")
        except Exception as e:
            logger.error(f"❌ 千问服务初始化失败: {str(e)}")
    else:
        logger.warning("⚠️ 千问API密钥未配置")
    
    # 备用AI配置
    if settings.openai_api_key:
        logger.info(f"✅ OpenAI API密钥已配置: {settings.openai_api_key[:10]}...")
    else:
        logger.info("ℹ️ OpenAI API密钥未配置（备用）")
    
    if settings.anthropic_api_key:
        logger.info(f"✅ Anthropic API密钥已配置: {settings.anthropic_api_key[:10]}...")
    else:
        logger.info("ℹ️ Anthropic API密钥未配置（备用）")


def test_feishu_config():
    """测试飞书配置"""
    logger.info("=== 飞书配置测试 ===")
    
    if settings.feishu_app_id:
        logger.info(f"✅ 飞书App ID已配置: {settings.feishu_app_id}")
    else:
        logger.warning("⚠️ 飞书App ID未配置")
    
    if settings.feishu_app_secret:
        logger.info(f"✅ 飞书App Secret已配置: {settings.feishu_app_secret[:10]}...")
    else:
        logger.warning("⚠️ 飞书App Secret未配置")
    
    if settings.feishu_bot_token:
        logger.info(f"✅ 飞书Bot Token已配置: {settings.feishu_bot_token[:10]}...")
    else:
        logger.warning("⚠️ 飞书Bot Token未配置")


def test_service_config():
    """测试服务配置"""
    logger.info("=== 服务配置测试 ===")
    logger.info(f"服务主机: {settings.server_host}")
    logger.info(f"服务端口: {settings.server_port}")
    logger.info(f"调试模式: {settings.debug}")
    logger.info(f"日志级别: {settings.log_level}")
    logger.info(f"上传目录: {settings.upload_dir}")


def main():
    """主函数"""
    logger.info("开始配置测试...")
    
    # 测试各项配置
    test_database_config()
    print()
    test_ai_config()
    print()
    test_feishu_config()
    print()
    test_service_config()
    
    logger.info("配置测试完成！")


if __name__ == "__main__":
    main()
