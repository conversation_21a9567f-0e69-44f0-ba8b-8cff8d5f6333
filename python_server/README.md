# PRD2TestCase 后端服务

基于FastAPI构建的PRD文档转测试用例的后端服务，支持多种文档格式读取、AI内容处理和实时流式响应。

## 功能特性

### 📄 文档读取
- **多格式支持**: 支持飞书文档、PDF、Word、Excel、PowerPoint等格式
- **智能识别**: 自动判断文档类型并选择合适的解析方式
- **权限处理**: 完善的错误处理和权限问题反馈
- **元数据提取**: 自动提取文档标题、作者、创建时间等信息

### 🤖 AI内容处理
- **智能总结**: 使用AI对文档内容进行总结和关键信息提取
- **测试用例生成**: 根据PRD内容自动生成全面的测试用例
- **多模型支持**: 支持OpenAI GPT和Anthropic Claude模型
- **流式响应**: 通过SSE实现实时流式内容生成

### 📝 Markdown处理
- **格式验证**: 校验Markdown文档的语法和结构合法性
- **JSON转换**: 将Markdown内容转换为结构化JSON数据
- **哈希计算**: 计算内容哈希值用于版本控制和去重
- **测试用例解析**: 从Markdown中提取结构化测试用例

### 💾 数据管理
- **多版本支持**: 同一PRD链接可保存多个版本记录
- **完整追踪**: 记录文档处理的完整生命周期
- **关联管理**: 测试用例与PRD文档的关联关系管理

### 🔔 通知集成
- **飞书机器人**: 自动发送任务完成通知
- **个性化消息**: 根据用户和项目定制通知内容
- **交互按钮**: 支持"前往查看"等交互功能

## 项目结构

```
python_server/
├── app/                    # 应用主目录
│   ├── api/               # API接口层
│   │   ├── endpoints/     # 具体接口实现
│   │   └── routes.py      # 路由配置
│   ├── core/              # 核心模块
│   │   └── database.py    # 数据库配置
│   ├── models/            # 数据模型
│   │   ├── prd_document.py
│   │   └── test_case.py
│   ├── services/          # 业务服务层
│   │   ├── document_reader.py    # 文档读取
│   │   ├── qwen_service.py       # 千问AI服务
│   │   └── feishu_service.py     # 飞书集成
│   └── utils/             # 工具类
│       ├── file_utils.py  # 文件处理工具
│       └── logger.py      # 日志配置
├── tests/                 # 测试文件
├── logs/                  # 日志目录
├── uploads/               # 文件上传目录
├── config.py              # 配置文件
├── main.py                # 应用入口
├── requirements.txt       # 依赖列表
├── Dockerfile            # Docker配置
├── docker-compose.yml    # Docker编排
└── run.sh                # 启动脚本
```

## 快速开始

### 环境要求
- Python 3.8+
- MySQL 5.7+ (主要数据库)
- Redis (可选，用于缓存)
- 阿里云DashScope API密钥（千问大模型）

### 安装和运行

1. **克隆项目**
```bash
cd python_server
```

2. **配置环境**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库、AI API密钥等
```

3. **运行服务**
```bash
# 使用启动脚本（推荐）
./run.sh

# 或手动运行
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
python main.py
```

4. **使用Docker**
```bash
# 单容器运行
docker build -t prd2testcase .
docker run -p 8000:8000 prd2testcase

# 完整环境（包含数据库）
docker-compose up -d
```

### API文档
服务启动后访问：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 主要API接口

### 文档处理
- `POST /api/v1/documents/process` - 处理PRD文档
- `GET /api/v1/documents/stream/{document_id}` - 流式生成测试用例
- `GET /api/v1/documents/{document_id}` - 获取文档信息

### 测试用例
- `GET /api/v1/testcases/search` - 根据PRD链接搜索测试用例
- `GET /api/v1/testcases/document/{document_id}` - 获取文档的测试用例
- `POST /api/v1/testcases/generate/{document_id}` - 生成测试用例记录

### 健康检查
- `GET /api/v1/health/` - 基础健康检查
- `GET /api/v1/health/detailed` - 详细健康检查

## 测试用例字段说明

### 核心字段
- **标题**: 测试用例的简洁标题
- **优先级**: P0（最高）、P1（重要）、P2（一般）
  - P0: 核心业务流程，必须通过的用例
  - P1: 重要功能，影响用户体验的用例
  - P2: 一般功能，辅助性功能的用例
- **是否需要自测**: 是/否
  - P0用例自动设置为"是"
  - P1、P2用例可根据实际情况设置
- **描述信息**: 详细的测试目标和场景描述

### 自动化规则
- 当优先级设置为P0时，系统自动将"是否需要自测"设置为"是"
- 支持从Markdown格式的测试用例中自动解析这些字段
- AI生成的测试用例会根据功能重要性自动分配合适的优先级

## 配置说明

### 环境变量
```bash
# 数据库配置
DB_HOST=**************
DB_PORT=3566
DB_USER=root
DB_PASSWORD=immotorsAI123456
DB_NAME=prd2testcase_dev
DB_DRIVER=mysql+pymysql

# 飞书配置
FEISHU_APP_ID=your_app_id
FEISHU_APP_SECRET=your_app_secret
FEISHU_BOT_TOKEN=your_bot_token

# AI模型配置 - 阿里千问（主要使用）
DASHSCOPE_API_KEY=your_dashscope_api_key
QWEN_MODEL=qwen-turbo
QWEN_BASE_URL=https://dashscope.aliyuncs.com/api/v1

# 备用AI配置
OPENAI_API_KEY=your_openai_key
ANTHROPIC_API_KEY=your_anthropic_key

# 服务配置
SERVER_HOST=0.0.0.0
SERVER_PORT=8000
DEBUG=True
```

## 开发和测试

### 运行测试
```bash
# 运行所有测试
pytest

# 运行特定测试文件
pytest tests/test_document_reader.py

# 生成覆盖率报告
pytest --cov=app tests/
```

### 开发建议
- 使用虚拟环境隔离依赖
- 遵循PEP 8代码规范
- 编写单元测试覆盖核心功能
- 使用类型注解提高代码可读性

## 部署

### Docker部署
```bash
# 构建镜像
docker build -t prd2testcase:latest .

# 运行容器
docker run -d \
  --name prd2testcase \
  -p 8000:8000 \
  -e DATABASE_URL=your_db_url \
  -e OPENAI_API_KEY=your_key \
  prd2testcase:latest
```

### 生产环境建议
- 使用PostgreSQL作为数据库
- 配置Redis用于缓存
- 使用Nginx作为反向代理
- 配置SSL证书
- 设置日志轮转和监控

## 故障排除

### 常见问题
1. **文档读取失败**: 检查网络连接和权限配置
2. **AI服务不可用**: 验证API密钥和网络访问
3. **数据库连接错误**: 检查数据库配置和连接字符串
4. **飞书通知失败**: 验证飞书应用配置和权限

### 日志查看
```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/app_error.log
```

## 许可证

MIT License
