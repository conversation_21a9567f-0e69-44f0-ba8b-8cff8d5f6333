编写后端服务，大致功能如下：

读取在线文档
判断在线文档是飞书或者其它接口
如果是飞书文档，则调用读取飞书文档的接口
如果是二进制文档，则下载文档，判断文档的类型
读取离线文档内容
读取不成功则返回给前端，告诉原因，是权限问题还是其它问题

根据文档使用AI总结内容，生成文档标题，修改时间、修改人等字段
查找文档内的图片和figma链接等信息
添加读取文件内容的接口，通过文件的url读取

根据文档内容拼接系统提示词
把提示词发送给大模型
处理大模型返回的markdown内容通过sse实时转发给前端

校验md文档的合法性，把md内容转换为json列表，并计算md的hash值

把prd文档链接和内容、作者、创建时间、修改时间、文档修改时间、还有生成的markdown内容、转换为json的列表、提示词，上传到数据库
同一个prd链接可以有多条记录，每条记录对应一个markdown文件的hash值

添加测试用例查找接口，根据prd文档链接，查找对应的记录，并返回记录列表

把生成的结果通过飞书机器人发送给生成的人：
飞书通知
- 生成完成
  - 标题：AI自动生成测试用例-已完成；
  - 发送给：任务创建人；
  - 包含内容：用例集（TS特有）/关联需求（Muggle特有）；
  - 模版：用例集/关联需求+“AI自动生成测试用例任务已完成，请前往查看。”；
  - TS样例：ugc帖子标题推荐 AI自动生成测试用例任务已完成，请前往查看。
    - 点击“前往查看”按钮：
      - 未登录先跳转到登录页；
      - 已登录或登录成功，浏览器打开“用例库”列表页，弹出对应AI自动生成测试用例弹窗；
