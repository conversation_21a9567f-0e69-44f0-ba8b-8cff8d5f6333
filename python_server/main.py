"""
主应用入口文件
"""
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger

from config import settings
from app.api.routes import api_router
from app.core.database import init_db


def create_app() -> FastAPI:
    """创建FastAPI应用实例"""
    app = FastAPI(
        title="PRD2TestCase API",
        description="PRD文档转测试用例的后端服务",
        version="1.0.0",
        debug=settings.debug
    )
    
    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 生产环境应该限制具体域名
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 注册路由
    app.include_router(api_router, prefix="/api/v1")
    
    # 启动事件
    @app.on_event("startup")
    async def startup_event():
        """应用启动时的初始化操作"""
        logger.info("应用启动中...")
        await init_db()
        logger.info("数据库初始化完成")
        logger.info("应用启动完成")
    
    # 关闭事件
    @app.on_event("shutdown")
    async def shutdown_event():
        """应用关闭时的清理操作"""
        logger.info("应用正在关闭...")
    
    return app


app = create_app()


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.server_host,
        port=settings.server_port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
