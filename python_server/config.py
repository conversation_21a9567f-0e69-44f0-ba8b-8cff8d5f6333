"""
应用配置模块
"""
import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """应用配置类"""
    
    # 数据库配置
    database_url: str = Field(default="sqlite:///./test.db", env="DATABASE_URL")
    
    # 飞书配置
    feishu_app_id: str = Field(default="", env="FEISHU_APP_ID")
    feishu_app_secret: str = Field(default="", env="FEISHU_APP_SECRET")
    feishu_bot_token: str = Field(default="", env="FEISHU_BOT_TOKEN")
    
    # AI模型配置
    openai_api_key: str = Field(default="", env="OPENAI_API_KEY")
    openai_base_url: str = Field(default="https://api.openai.com/v1", env="OPENAI_BASE_URL")
    anthropic_api_key: str = Field(default="", env="ANTHROPIC_API_KEY")
    
    # 服务配置
    server_host: str = Field(default="0.0.0.0", env="SERVER_HOST")
    server_port: int = Field(default=8000, env="SERVER_PORT")
    debug: bool = Field(default=True, env="DEBUG")
    
    # 文件存储配置
    upload_dir: str = Field(default="./uploads", env="UPLOAD_DIR")
    max_file_size: str = Field(default="50MB", env="MAX_FILE_SIZE")
    
    # 日志配置
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_file: str = Field(default="./logs/app.log", env="LOG_FILE")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# 全局配置实例
settings = Settings()
