"""
应用配置模块
"""
import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """应用配置类"""

    # 数据库配置
    db_host: str = Field(default="localhost", env="DB_HOST")
    db_port: int = Field(default=3306, env="DB_PORT")
    db_user: str = Field(default="root", env="DB_USER")
    db_password: str = Field(default="", env="DB_PASSWORD")
    db_name: str = Field(default="prd2testcase_dev", env="DB_NAME")
    db_driver: str = Field(default="mysql+pymysql", env="DB_DRIVER")
    database_url: str = Field(default="", env="DATABASE_URL")

    @property
    def get_database_url(self) -> str:
        """构建数据库连接URL"""
        if self.database_url:
            return self.database_url
        return f"{self.db_driver}://{self.db_user}:{self.db_password}@{self.db_host}:{self.db_port}/{self.db_name}"
    
    # 飞书配置
    feishu_app_id: str = Field(default="", env="FEISHU_APP_ID")
    feishu_app_secret: str = Field(default="", env="FEISHU_APP_SECRET")
    feishu_bot_token: str = Field(default="", env="FEISHU_BOT_TOKEN")
    
    # AI模型配置 - 阿里千问
    dashscope_api_key: str = Field(default="", env="DASHSCOPE_API_KEY")
    qwen_model: str = Field(default="qwen-turbo", env="QWEN_MODEL")
    qwen_base_url: str = Field(default="https://dashscope.aliyuncs.com/api/v1", env="QWEN_BASE_URL")

    # 备用AI配置
    openai_api_key: str = Field(default="", env="OPENAI_API_KEY")
    openai_base_url: str = Field(default="https://api.openai.com/v1", env="OPENAI_BASE_URL")
    anthropic_api_key: str = Field(default="", env="ANTHROPIC_API_KEY")
    
    # 服务配置
    server_host: str = Field(default="0.0.0.0", env="SERVER_HOST")
    server_port: int = Field(default=8000, env="SERVER_PORT")
    debug: bool = Field(default=True, env="DEBUG")
    
    # 文件存储配置
    upload_dir: str = Field(default="./uploads", env="UPLOAD_DIR")
    max_file_size: str = Field(default="50MB", env="MAX_FILE_SIZE")
    
    # 日志配置
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_file: str = Field(default="./logs/app.log", env="LOG_FILE")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# 全局配置实例
settings = Settings()
