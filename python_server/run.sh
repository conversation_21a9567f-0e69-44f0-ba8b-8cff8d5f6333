#!/bin/bash

# PRD2TestCase 后端服务启动脚本

set -e

echo "=== PRD2TestCase 后端服务启动 ==="

# 检查Python版本
python_version=$(python3 --version 2>&1 | awk '{print $2}' | cut -d. -f1,2)
required_version="3.8"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "错误: 需要Python $required_version 或更高版本，当前版本: $python_version"
    exit 1
fi

# 创建虚拟环境（如果不存在）
if [ ! -d "venv" ]; then
    echo "创建Python虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境
echo "激活虚拟环境..."
source venv/bin/activate

# 升级pip
echo "升级pip..."
pip install --upgrade pip

# 安装依赖
echo "安装Python依赖..."
pip install -r requirements.txt

# 创建必要的目录
echo "创建必要的目录..."
mkdir -p logs uploads/temp

# 检查环境变量配置
if [ ! -f ".env" ]; then
    echo "创建环境配置文件..."
    cp .env.example .env
    echo "请编辑 .env 文件配置相关参数"
fi

# 运行数据库迁移（如果需要）
echo "初始化数据库..."
# python -c "from app.core.database import init_db; import asyncio; asyncio.run(init_db())"

# 启动服务
echo "启动服务..."
echo "服务将在 http://localhost:8000 启动"
echo "API文档: http://localhost:8000/docs"
echo "按 Ctrl+C 停止服务"

python main.py
