"""
文档读取服务测试
"""
import pytest
from unittest.mock import Mock, patch
from app.services.document_reader import DocumentReader


@pytest.fixture
def document_reader():
    """创建文档读取器实例"""
    return DocumentReader()


@pytest.mark.asyncio
async def test_detect_feishu_document(document_reader):
    """测试飞书文档检测"""
    feishu_urls = [
        "https://bytedance.feishu.cn/docs/doccnAbCdEfGhIjKlMnOpQrStUvWxYz",
        "https://feishu.cn/docs/doccnAbCdEfGhIjKlMnOpQrStUvWxYz",
        "https://larksuite.com/docs/doccnAbCdEfGhIjKlMnOpQrStUvWxYz"
    ]
    
    for url in feishu_urls:
        doc_type = document_reader._detect_document_type(url)
        assert doc_type == "feishu"


@pytest.mark.asyncio
async def test_detect_binary_document(document_reader):
    """测试二进制文档检测"""
    binary_urls = [
        "https://example.com/document.pdf",
        "https://example.com/document.docx",
        "https://example.com/presentation.pptx",
        "https://example.com/spreadsheet.xlsx"
    ]
    
    for url in binary_urls:
        doc_type = document_reader._detect_document_type(url)
        assert doc_type == "binary"


@pytest.mark.asyncio
async def test_extract_feishu_doc_id(document_reader):
    """测试飞书文档ID提取"""
    test_cases = [
        ("https://bytedance.feishu.cn/docs/doccnAbCdEfGhIjKlMnOpQrStUvWxYz", "doccnAbCdEfGhIjKlMnOpQrStUvWxYz"),
        ("https://feishu.cn/wiki/wikcnAbCdEfGhIjKlMnOpQrStUvWxYz", "wikcnAbCdEfGhIjKlMnOpQrStUvWxYz"),
        ("https://example.com/share?token=AbCdEfGhIjKlMnOpQrStUvWxYz", "AbCdEfGhIjKlMnOpQrStUvWxYz")
    ]
    
    for url, expected_id in test_cases:
        doc_id = document_reader._extract_feishu_doc_id(url)
        assert doc_id == expected_id


@pytest.mark.asyncio
async def test_read_document_error_handling(document_reader):
    """测试文档读取错误处理"""
    # 测试无效URL
    result = await document_reader.read_document("invalid-url")
    assert result["success"] is False
    assert "error" in result


if __name__ == "__main__":
    pytest.main([__file__])
