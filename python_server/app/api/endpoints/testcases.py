"""
测试用例接口
"""
from fastapi import APIRouter, HTTPException, Depends, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from pydantic import BaseModel
from loguru import logger

from app.core.database import get_db
from app.models.prd_document import PRDDocument
from app.models.test_case import TestCase
from app.services.qwen_service import QwenService

router = APIRouter()


class TestCaseResponse(BaseModel):
    """测试用例响应模型"""
    id: int
    case_title: str
    case_description: str = None
    case_type: str = None
    priority: str  # P0、P1、P2等
    need_self_test: str  # 是否需要自测：是/否
    test_steps: List[dict] = None
    expected_result: str = None
    test_data: dict = None
    preconditions: str = None
    postconditions: str = None
    status: str
    created_at: str
    updated_at: str


class DocumentTestCasesResponse(BaseModel):
    """文档测试用例列表响应模型"""
    document_id: int
    document_url: str
    document_title: str
    total_cases: int
    test_cases: List[TestCaseResponse]


@router.get("/search")
async def search_testcases(
    prd_url: str = Query(..., description="PRD文档链接"),
    status: Optional[str] = Query(None, description="状态筛选"),
    priority: Optional[str] = Query(None, description="优先级筛选"),
    limit: int = Query(50, description="返回数量限制"),
    offset: int = Query(0, description="偏移量"),
    db: Session = Depends(get_db)
):
    """
    根据PRD文档链接查找测试用例
    
    Args:
        prd_url: PRD文档链接
        status: 状态筛选
        priority: 优先级筛选
        limit: 返回数量限制
        offset: 偏移量
        db: 数据库会话
        
    Returns:
        测试用例列表
    """
    try:
        # 查找PRD文档记录
        prd_docs = db.query(PRDDocument).filter(PRDDocument.url == prd_url).all()
        
        if not prd_docs:
            raise HTTPException(status_code=404, detail="未找到相关PRD文档")
        
        results = []
        
        for prd_doc in prd_docs:
            # 构建查询
            query = db.query(TestCase).filter(TestCase.prd_document_id == prd_doc.id)
            
            # 添加筛选条件
            if status:
                query = query.filter(TestCase.status == status)
            if priority:
                query = query.filter(TestCase.priority == priority)
            
            # 获取测试用例
            test_cases = query.offset(offset).limit(limit).all()
            
            # 转换为响应格式
            test_case_responses = []
            for tc in test_cases:
                test_case_responses.append(TestCaseResponse(
                    id=tc.id,
                    case_title=tc.case_title,
                    case_description=tc.case_description,
                    case_type=tc.case_type,
                    priority=tc.priority,
                    need_self_test=tc.need_self_test,
                    test_steps=tc.test_steps or [],
                    expected_result=tc.expected_result,
                    test_data=tc.test_data or {},
                    preconditions=tc.preconditions,
                    postconditions=tc.postconditions,
                    status=tc.status,
                    created_at=tc.created_at.isoformat() if tc.created_at else "",
                    updated_at=tc.updated_at.isoformat() if tc.updated_at else ""
                ))
            
            results.append(DocumentTestCasesResponse(
                document_id=prd_doc.id,
                document_url=prd_doc.url,
                document_title=prd_doc.title or "未命名文档",
                total_cases=len(test_case_responses),
                test_cases=test_case_responses
            ))
        
        return {
            "success": True,
            "total_documents": len(results),
            "results": results
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"搜索测试用例失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/document/{document_id}")
async def get_document_testcases(
    document_id: int,
    status: Optional[str] = Query(None, description="状态筛选"),
    priority: Optional[str] = Query(None, description="优先级筛选"),
    db: Session = Depends(get_db)
):
    """
    获取指定文档的测试用例
    
    Args:
        document_id: 文档ID
        status: 状态筛选
        priority: 优先级筛选
        db: 数据库会话
        
    Returns:
        测试用例列表
    """
    try:
        # 检查文档是否存在
        prd_doc = db.query(PRDDocument).filter(PRDDocument.id == document_id).first()
        if not prd_doc:
            raise HTTPException(status_code=404, detail="文档不存在")
        
        # 构建查询
        query = db.query(TestCase).filter(TestCase.prd_document_id == document_id)
        
        # 添加筛选条件
        if status:
            query = query.filter(TestCase.status == status)
        if priority:
            query = query.filter(TestCase.priority == priority)
        
        # 获取测试用例
        test_cases = query.all()
        
        # 转换为响应格式
        test_case_responses = []
        for tc in test_cases:
            test_case_responses.append(TestCaseResponse(
                id=tc.id,
                case_title=tc.case_title,
                case_description=tc.case_description,
                case_type=tc.case_type,
                priority=tc.priority,
                need_self_test=tc.need_self_test,
                test_steps=tc.test_steps or [],
                expected_result=tc.expected_result,
                test_data=tc.test_data or {},
                preconditions=tc.preconditions,
                postconditions=tc.postconditions,
                status=tc.status,
                created_at=tc.created_at.isoformat() if tc.created_at else "",
                updated_at=tc.updated_at.isoformat() if tc.updated_at else ""
            ))
        
        return DocumentTestCasesResponse(
            document_id=prd_doc.id,
            document_url=prd_doc.url,
            document_title=prd_doc.title or "未命名文档",
            total_cases=len(test_case_responses),
            test_cases=test_case_responses
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文档测试用例失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/generate/{document_id}")
async def generate_testcases_from_markdown(
    document_id: int,
    db: Session = Depends(get_db)
):
    """
    从Markdown内容生成测试用例记录
    
    Args:
        document_id: 文档ID
        db: 数据库会话
        
    Returns:
        生成结果
    """
    try:
        # 获取文档记录
        prd_doc = db.query(PRDDocument).filter(PRDDocument.id == document_id).first()
        if not prd_doc:
            raise HTTPException(status_code=404, detail="文档不存在")
        
        if not prd_doc.markdown_content:
            raise HTTPException(status_code=400, detail="文档没有Markdown内容")
        
        # 解析Markdown中的测试用例
        qwen_service = QwenService()
        test_cases_data = qwen_service.extract_test_cases_from_markdown(prd_doc.markdown_content)
        
        if not test_cases_data:
            raise HTTPException(status_code=400, detail="未能从Markdown中提取到测试用例")
        
        # 删除现有的测试用例（如果需要重新生成）
        db.query(TestCase).filter(TestCase.prd_document_id == document_id).delete()
        
        # 创建新的测试用例记录
        created_cases = []
        for case_data in test_cases_data:
            priority = case_data.get("priority", "P2")
            # 确保优先级格式正确
            if priority.lower() in ["high", "高"]:
                priority = "P0"
            elif priority.lower() in ["medium", "中"]:
                priority = "P1"
            elif priority.lower() in ["low", "低"]:
                priority = "P2"

            test_case = TestCase(
                prd_document_id=document_id,
                case_title=case_data.get("title", ""),
                case_description=case_data.get("description", ""),
                priority=priority,
                test_steps=case_data.get("steps", []),
                expected_result=case_data.get("expected_result", ""),
                preconditions=case_data.get("preconditions", ""),
                test_data={"raw_data": case_data.get("test_data", "")},
                status="draft"
            )
            # 设置优先级会自动处理自测要求
            test_case.set_priority(priority)
            db.add(test_case)
            created_cases.append(test_case)
        
        db.commit()
        
        # 刷新对象以获取ID
        for case in created_cases:
            db.refresh(case)
        
        return {
            "success": True,
            "message": f"成功生成 {len(created_cases)} 个测试用例",
            "total_cases": len(created_cases),
            "case_ids": [case.id for case in created_cases]
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"生成测试用例记录失败: {str(e)}")
        db.rollback()
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{testcase_id}")
async def get_testcase(testcase_id: int, db: Session = Depends(get_db)):
    """
    获取单个测试用例详情
    
    Args:
        testcase_id: 测试用例ID
        db: 数据库会话
        
    Returns:
        测试用例详情
    """
    try:
        test_case = db.query(TestCase).filter(TestCase.id == testcase_id).first()
        if not test_case:
            raise HTTPException(status_code=404, detail="测试用例不存在")
        
        return TestCaseResponse(
            id=test_case.id,
            case_title=test_case.case_title,
            case_description=test_case.case_description,
            case_type=test_case.case_type,
            priority=test_case.priority,
            need_self_test=test_case.need_self_test,
            test_steps=test_case.test_steps or [],
            expected_result=test_case.expected_result,
            test_data=test_case.test_data or {},
            preconditions=test_case.preconditions,
            postconditions=test_case.postconditions,
            status=test_case.status,
            created_at=test_case.created_at.isoformat() if test_case.created_at else "",
            updated_at=test_case.updated_at.isoformat() if test_case.updated_at else ""
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取测试用例详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
