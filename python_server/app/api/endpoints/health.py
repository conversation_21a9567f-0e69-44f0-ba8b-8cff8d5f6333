"""
健康检查接口
"""
from fastapi import APIRouter
from datetime import datetime

router = APIRouter()


@router.get("/")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "PRD2TestCase API",
        "version": "1.0.0"
    }


@router.get("/detailed")
async def detailed_health_check():
    """详细健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "PRD2TestCase API",
        "version": "1.0.0",
        "components": {
            "database": "connected",
            "ai_service": "available",
            "file_storage": "accessible"
        }
    }
