"""
文档处理接口
"""
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from typing import Dict, Any, List
from pydantic import BaseModel, HttpUrl
from loguru import logger

from app.core.database import get_db
from app.models.prd_document import PRDDocument
from app.services.document_reader import DocumentReader
from app.services.ai_service import AIService
from app.services.llm_service import LLMService
from app.services.markdown_service import MarkdownService
from app.services.feishu_service import FeishuService

router = APIRouter()


class DocumentProcessRequest(BaseModel):
    """文档处理请求模型"""
    url: HttpUrl
    user_id: str = ""
    requirements: List[str] = []


class DocumentResponse(BaseModel):
    """文档响应模型"""
    success: bool
    message: str
    document_id: int = None
    error: str = None


@router.post("/process", response_model=DocumentResponse)
async def process_document(
    request: DocumentProcessRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """
    处理文档并生成测试用例
    
    Args:
        request: 文档处理请求
        background_tasks: 后台任务
        db: 数据库会话
        
    Returns:
        处理结果
    """
    try:
        # 创建文档记录
        prd_doc = PRDDocument(
            url=str(request.url),
            status="processing"
        )
        db.add(prd_doc)
        db.commit()
        db.refresh(prd_doc)
        
        # 添加后台任务处理文档
        background_tasks.add_task(
            process_document_background,
            prd_doc.id,
            str(request.url),
            request.user_id,
            request.requirements
        )
        
        return DocumentResponse(
            success=True,
            message="文档处理任务已启动",
            document_id=prd_doc.id
        )
        
    except Exception as e:
        logger.error(f"启动文档处理任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/stream/{document_id}")
async def stream_testcase_generation(document_id: int, db: Session = Depends(get_db)):
    """
    流式生成测试用例
    
    Args:
        document_id: 文档ID
        db: 数据库会话
        
    Returns:
        SSE流式响应
    """
    try:
        # 获取文档记录
        prd_doc = db.query(PRDDocument).filter(PRDDocument.id == document_id).first()
        if not prd_doc:
            raise HTTPException(status_code=404, detail="文档不存在")
        
        if not prd_doc.content:
            raise HTTPException(status_code=400, detail="文档内容为空")
        
        # 创建LLM服务实例
        llm_service = LLMService()
        
        # 生成系统提示词
        doc_metadata = {
            "title": prd_doc.title or "",
            "author": prd_doc.author or "",
            "doc_type": "prd",
            "images": prd_doc.images or [],
            "figma_links": prd_doc.figma_links or []
        }
        system_prompt = await llm_service.generate_system_prompt(doc_metadata)
        
        # 返回流式响应
        return StreamingResponse(
            llm_service.generate_testcases_stream(prd_doc.content, system_prompt),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Content-Type": "text/event-stream"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"流式生成测试用例失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{document_id}")
async def get_document(document_id: int, db: Session = Depends(get_db)):
    """
    获取文档信息
    
    Args:
        document_id: 文档ID
        db: 数据库会话
        
    Returns:
        文档信息
    """
    try:
        prd_doc = db.query(PRDDocument).filter(PRDDocument.id == document_id).first()
        if not prd_doc:
            raise HTTPException(status_code=404, detail="文档不存在")
        
        return {
            "id": prd_doc.id,
            "url": prd_doc.url,
            "title": prd_doc.title,
            "author": prd_doc.author,
            "status": prd_doc.status,
            "created_at": prd_doc.created_at,
            "updated_at": prd_doc.updated_at,
            "doc_created_at": prd_doc.doc_created_at,
            "doc_updated_at": prd_doc.doc_updated_at,
            "content_hash": prd_doc.content_hash,
            "error_message": prd_doc.error_message,
            "images": prd_doc.images,
            "figma_links": prd_doc.figma_links
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文档信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{document_id}/content")
async def get_document_content(document_id: int, db: Session = Depends(get_db)):
    """
    获取文档内容
    
    Args:
        document_id: 文档ID
        db: 数据库会话
        
    Returns:
        文档内容
    """
    try:
        prd_doc = db.query(PRDDocument).filter(PRDDocument.id == document_id).first()
        if not prd_doc:
            raise HTTPException(status_code=404, detail="文档不存在")
        
        return {
            "id": prd_doc.id,
            "content": prd_doc.content,
            "markdown_content": prd_doc.markdown_content,
            "json_content": prd_doc.json_content
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文档内容失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


async def process_document_background(
    document_id: int,
    url: str,
    user_id: str,
    requirements: List[str]
):
    """
    后台处理文档任务
    
    Args:
        document_id: 文档ID
        url: 文档URL
        user_id: 用户ID
        requirements: 特殊需求
    """
    from app.core.database import SessionLocal
    
    db = SessionLocal()
    try:
        # 获取文档记录
        prd_doc = db.query(PRDDocument).filter(PRDDocument.id == document_id).first()
        if not prd_doc:
            logger.error(f"文档记录不存在: {document_id}")
            return
        
        # 读取文档内容
        document_reader = DocumentReader()
        read_result = await document_reader.read_document(url)
        
        if not read_result["success"]:
            prd_doc.status = "failed"
            prd_doc.error_message = read_result["error"]
            db.commit()
            return
        
        # 更新文档基本信息
        prd_doc.content = read_result["content"]
        prd_doc.title = read_result.get("title", "")
        prd_doc.author = read_result.get("author", "")
        prd_doc.doc_created_at = read_result.get("created_at")
        prd_doc.doc_updated_at = read_result.get("updated_at")
        prd_doc.images = read_result.get("images", [])
        prd_doc.figma_links = read_result.get("figma_links", [])
        
        # AI总结文档
        ai_service = AIService()
        summary_result = await ai_service.summarize_document(prd_doc.content)
        
        if summary_result["success"]:
            if not prd_doc.title:
                prd_doc.title = summary_result["title"]
            if not prd_doc.author:
                prd_doc.author = summary_result["author"]
        
        # 生成测试用例
        testcase_result = await ai_service.generate_test_cases(prd_doc.content, requirements)
        
        if testcase_result["success"]:
            prd_doc.markdown_content = testcase_result["markdown_content"]
            
            # 处理Markdown内容
            markdown_service = MarkdownService()
            
            # 验证Markdown
            validation_result = markdown_service.validate_markdown(prd_doc.markdown_content)
            
            # 转换为JSON
            json_result = markdown_service.convert_to_json(prd_doc.markdown_content)
            if json_result["success"]:
                prd_doc.json_content = json_result["data"]
            
            # 计算哈希值
            prd_doc.content_hash = markdown_service.calculate_hash(prd_doc.markdown_content)
            
            prd_doc.status = "completed"
        else:
            prd_doc.status = "failed"
            prd_doc.error_message = testcase_result["error"]
        
        db.commit()
        
        # 发送飞书通知
        if user_id and prd_doc.status == "completed":
            feishu_service = FeishuService()
            await feishu_service.send_notification(
                user_id,
                f"{prd_doc.title} AI自动生成测试用例任务已完成，请前往查看。",
                "AI自动生成测试用例-已完成"
            )
        
    except Exception as e:
        logger.error(f"后台处理文档失败: {str(e)}")
        prd_doc.status = "failed"
        prd_doc.error_message = str(e)
        db.commit()
    finally:
        db.close()
