"""
阿里千问AI服务
"""
import json
from typing import Dict, Any, List, Optional, AsyncGenerator
import dashscope
from dashscope import Generation
from loguru import logger

from config import settings


class QwenService:
    """阿里千问AI服务类"""
    
    def __init__(self):
        # 设置API密钥
        dashscope.api_key = settings.dashscope_api_key
        self.model = settings.qwen_model
    
    async def summarize_document(self, content: str, doc_type: str = "prd") -> Dict[str, Any]:
        """
        总结文档内容
        
        Args:
            content: 文档内容
            doc_type: 文档类型
            
        Returns:
            总结结果
        """
        try:
            prompt = self._build_summary_prompt(content, doc_type)
            
            response = Generation.call(
                model=self.model,
                prompt=prompt,
                temperature=0.3,
                max_tokens=1000,
                top_p=0.8
            )
            
            if response.status_code == 200:
                content = response.output.text
                summary_data = self._parse_summary_response(content)
                return {
                    "success": True,
                    "title": summary_data.get("title", ""),
                    "summary": summary_data.get("summary", ""),
                    "key_points": summary_data.get("key_points", []),
                    "author": summary_data.get("author", ""),
                    "update_time": summary_data.get("update_time", "")
                }
            else:
                return {
                    "success": False,
                    "error": f"千问API调用失败: {response.message}"
                }
                
        except Exception as e:
            logger.error(f"千问文档总结失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _build_summary_prompt(self, content: str, doc_type: str) -> str:
        """构建总结提示词"""
        base_prompt = f"""
请分析以下{doc_type}文档内容，并提供结构化的总结信息。

文档内容：
{content} 
请按照以下JSON格式返回结果：
{{
    "title": "文档标题",
    "summary": "文档摘要（200字以内）",
    "key_points": ["关键点1", "关键点2", "关键点3"],
    "author": "作者信息（如果能识别）",
    "update_time": "更新时间（如果能识别）"
}}

要求：
1. 标题要简洁明了，体现文档核心内容
2. 摘要要涵盖文档的主要内容和目标
3. 关键点要提取3-5个最重要的信息
4. 如果无法识别作者或时间信息，请返回空字符串
5. 返回的必须是有效的JSON格式
"""
        return base_prompt
    
    def _parse_summary_response(self, response: str) -> Dict[str, Any]:
        """解析总结响应"""
        try:
            # 尝试直接解析JSON
            return json.loads(response)
        except json.JSONDecodeError:
            # 如果不是有效JSON，尝试提取JSON部分
            try:
                import re
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    return json.loads(json_match.group())
            except:
                pass
            
            # 如果都失败了，返回默认结构
            return {
                "title": "文档标题提取失败",
                "summary": response[:200] if response else "总结生成失败",
                "key_points": [],
                "author": "",
                "update_time": ""
            }
    
    async def generate_test_cases(self, prd_content: str, requirements: List[str] = None) -> Dict[str, Any]:
        """
        根据PRD内容生成测试用例
        
        Args:
            prd_content: PRD文档内容
            requirements: 特定需求列表
            
        Returns:
            测试用例生成结果
        """
        try:
            prompt = self._build_testcase_prompt(prd_content, requirements)
            
            response = Generation.call(
                model=self.model,
                prompt=prompt,
                temperature=0.3,
                max_tokens=4000,
                top_p=0.8
            )
            
            if response.status_code == 200:
                return {
                    "success": True,
                    "markdown_content": response.output.text,
                    "model": "qwen"
                }
            else:
                return {
                    "success": False,
                    "error": f"千问API调用失败: {response.message}"
                }
                
        except Exception as e:
            logger.error(f"千问测试用例生成失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _build_testcase_prompt(self, prd_content: str, requirements: List[str] = None) -> str:
        """构建测试用例生成提示词"""
        requirements_text = ""
        if requirements:
            requirements_text = f"\n特别关注以下需求：\n" + "\n".join([f"- {req}" for req in requirements])
        
        prompt = f"""
请根据以下PRD文档内容，生成详细的测试用例。

PRD文档内容：
{prd_content}  
{requirements_text}

请按照以下Markdown格式生成测试用例，并为每个测试用例分配合适的优先级（P0、P1、P2）：

# 测试用例集

## 功能测试用例

### 用例1：[功能名称]
- **用例标题**：[简洁的标题]
- **用例描述**：[详细描述测试目标]
- **优先级**：P0/P1/P2（P0为最高优先级，核心功能；P1为重要功能；P2为一般功能）
- **是否需要自测**：是/否（P0用例必须为"是"）
- **前置条件**：[测试前需要满足的条件]
- **测试步骤**：
  1. [步骤1]
  2. [步骤2]
  3. [步骤3]
- **预期结果**：[期望的测试结果]
- **测试数据**：[如果需要特定测试数据]

## 边界测试用例

### 用例2：[边界场景]
[按照上述格式继续...]

## 异常测试用例

### 用例3：[异常场景]
[按照上述格式继续...]

要求：
1. 根据PRD内容识别所有主要功能点
2. 为每个功能点生成正常流程、边界条件和异常情况的测试用例
3. 合理分配优先级：核心业务流程为P0，重要功能为P1，辅助功能为P2
4. P0用例必须标记为需要自测
5. 测试步骤要具体可执行
6. 预期结果要明确可验证
7. 至少生成10个测试用例，包含不同优先级
"""
        return prompt
    
    async def generate_testcases_stream(self, prd_content: str, system_prompt: str = None) -> AsyncGenerator[str, None]:
        """
        流式生成测试用例（千问暂不支持流式，模拟流式响应）
        
        Args:
            prd_content: PRD文档内容
            system_prompt: 系统提示词
            
        Yields:
            流式响应数据
        """
        try:
            # 发送开始信号
            yield self._format_sse_data({
                "type": "start",
                "message": "开始生成测试用例..."
            })
            
            # 构建完整的提示词
            full_prompt = self._build_complete_prompt(prd_content, system_prompt)
            
            # 调用千问API
            response = Generation.call(
                model=self.model,
                prompt=full_prompt,
                temperature=0.3,
                max_tokens=4000,
                top_p=0.8
            )
            
            if response.status_code == 200:
                content = response.output.text
                
                # 模拟流式输出，分段发送内容
                chunk_size = 100
                for i in range(0, len(content), chunk_size):
                    chunk = content[i:i + chunk_size]
                    yield self._format_sse_data({
                        "type": "content",
                        "content": chunk
                    })
                    # 添加小延迟模拟流式效果
                    import asyncio
                    await asyncio.sleep(0.1)
                
                # 发送完成信号
                yield self._format_sse_data({
                    "type": "complete",
                    "message": "测试用例生成完成"
                })
            else:
                yield self._format_sse_data({
                    "type": "error",
                    "message": f"千问API调用失败: {response.message}"
                })
                
        except Exception as e:
            logger.error(f"千问流式生成失败: {str(e)}")
            yield self._format_sse_data({
                "type": "error",
                "message": str(e)
            })
    
    def _build_complete_prompt(self, prd_content: str, system_prompt: str = None) -> str:
        """构建完整的提示词"""
        if system_prompt:
            base_prompt = system_prompt
        else:
            base_prompt = """
你是一个专业的测试工程师，擅长根据产品需求文档(PRD)生成全面的测试用例。

请根据提供的PRD内容，生成详细的测试用例集合。测试用例应该包括：
1. 功能测试用例 - 验证核心功能是否正常工作
2. 边界测试用例 - 验证边界条件和极限情况
3. 异常测试用例 - 验证错误处理和异常情况
4. 用户体验测试用例 - 验证用户交互和体验

每个测试用例应包含：
- 用例标题
- 用例描述
- 优先级（P0/P1/P2）
- 是否需要自测（是/否，P0必须为是）
- 前置条件
- 测试步骤（详细可执行）
- 预期结果
- 测试数据（如需要）

请使用Markdown格式输出，结构清晰，便于阅读和执行。
"""
        
        full_prompt = f"{base_prompt}\n\nPRD文档内容：\n{prd_content[:8000]}"  # 限制长度
        return full_prompt
    
    def _format_sse_data(self, data: Dict[str, Any]) -> str:
        """格式化SSE数据"""
        return f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
