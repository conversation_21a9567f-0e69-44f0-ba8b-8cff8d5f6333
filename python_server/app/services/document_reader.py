"""
文档读取服务
"""
import os
import re
import httpx
import aiofiles
from typing import Optional, Dict, Any, Tuple
from urllib.parse import urlparse
from loguru import logger

from app.services.feishu_service import FeishuService
from app.utils.file_utils import FileUtils


class DocumentReader:
    """文档读取器"""
    
    def __init__(self):
        self.feishu_service = FeishuService()
        self.file_utils = FileUtils()
    
    async def read_document(self, url: str) -> Dict[str, Any]:
        """
        读取文档内容
        
        Args:
            url: 文档链接
            
        Returns:
            包含文档内容和元数据的字典
        """
        try:
            # 判断文档类型
            doc_type = self._detect_document_type(url)
            
            if doc_type == "feishu":
                return await self._read_feishu_document(url)
            elif doc_type == "binary":
                return await self._read_binary_document(url)
            else:
                return await self._read_online_document(url)
                
        except Exception as e:
            logger.error(f"读取文档失败: {url}, 错误: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "error_type": "read_error"
            }
    
    def _detect_document_type(self, url: str) -> str:
        """
        检测文档类型
        
        Args:
            url: 文档链接
            
        Returns:
            文档类型: feishu, binary, online
        """
        # 飞书文档检测
        feishu_patterns = [
            r'feishu\.cn',
            r'larksuite\.com',
            r'bytedance\.feishu\.cn'
        ]
        
        for pattern in feishu_patterns:
            if re.search(pattern, url, re.IGNORECASE):
                return "feishu"
        
        # 二进制文档检测（通过文件扩展名）
        binary_extensions = ['.pdf', '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx']
        parsed_url = urlparse(url)
        path = parsed_url.path.lower()
        
        for ext in binary_extensions:
            if path.endswith(ext):
                return "binary"
        
        return "online"
    
    async def _read_feishu_document(self, url: str) -> Dict[str, Any]:
        """
        读取飞书文档
        
        Args:
            url: 飞书文档链接
            
        Returns:
            文档内容和元数据
        """
        try:
            # 提取文档ID
            doc_id = self._extract_feishu_doc_id(url)
            if not doc_id:
                return {
                    "success": False,
                    "error": "无法提取飞书文档ID",
                    "error_type": "invalid_url"
                }
            
            # 调用飞书API读取文档
            result = await self.feishu_service.get_document_content(doc_id)
            
            if result["success"]:
                return {
                    "success": True,
                    "content": result["content"],
                    "title": result.get("title", ""),
                    "author": result.get("author", ""),
                    "created_at": result.get("created_at"),
                    "updated_at": result.get("updated_at"),
                    "images": result.get("images", []),
                    "figma_links": result.get("figma_links", []),
                    "doc_type": "feishu"
                }
            else:
                return {
                    "success": False,
                    "error": result["error"],
                    "error_type": result.get("error_type", "feishu_api_error")
                }
                
        except Exception as e:
            logger.error(f"读取飞书文档失败: {url}, 错误: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "error_type": "feishu_read_error"
            }
    
    def _extract_feishu_doc_id(self, url: str) -> Optional[str]:
        """
        从飞书链接中提取文档ID
        
        Args:
            url: 飞书文档链接
            
        Returns:
            文档ID或None
        """
        patterns = [
            r'/docs/([a-zA-Z0-9]+)',
            r'/wiki/([a-zA-Z0-9]+)',
            r'token=([a-zA-Z0-9]+)'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        
        return None

    async def _read_binary_document(self, url: str) -> Dict[str, Any]:
        """
        读取二进制文档

        Args:
            url: 文档链接

        Returns:
            文档内容和元数据
        """
        try:
            # 下载文档
            file_path = await self._download_file(url)
            if not file_path:
                return {
                    "success": False,
                    "error": "文档下载失败",
                    "error_type": "download_error"
                }

            # 解析文档内容
            content_result = await self.file_utils.extract_content(file_path)

            # 清理临时文件
            try:
                os.remove(file_path)
            except:
                pass

            if content_result["success"]:
                return {
                    "success": True,
                    "content": content_result["content"],
                    "title": content_result.get("title", ""),
                    "author": content_result.get("author", ""),
                    "created_at": content_result.get("created_at"),
                    "updated_at": content_result.get("updated_at"),
                    "images": content_result.get("images", []),
                    "figma_links": [],
                    "doc_type": "binary"
                }
            else:
                return {
                    "success": False,
                    "error": content_result["error"],
                    "error_type": "parse_error"
                }

        except Exception as e:
            logger.error(f"读取二进制文档失败: {url}, 错误: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "error_type": "binary_read_error"
            }

    async def _read_online_document(self, url: str) -> Dict[str, Any]:
        """
        读取在线文档（HTML等）

        Args:
            url: 文档链接

        Returns:
            文档内容和元数据
        """
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(url)
                response.raise_for_status()

                # 简单的HTML内容提取（可以使用BeautifulSoup等库进行更复杂的解析）
                content = response.text

                # 提取标题
                title_match = re.search(r'<title>(.*?)</title>', content, re.IGNORECASE | re.DOTALL)
                title = title_match.group(1).strip() if title_match else ""

                # 提取图片链接
                img_matches = re.findall(r'<img[^>]+src=["\']([^"\']+)["\']', content, re.IGNORECASE)
                images = [{"url": img, "alt": ""} for img in img_matches]

                # 提取Figma链接
                figma_matches = re.findall(r'https://[^"\s]*figma\.com[^"\s]*', content)
                figma_links = [{"url": link} for link in figma_matches]

                return {
                    "success": True,
                    "content": content,
                    "title": title,
                    "author": "",
                    "created_at": None,
                    "updated_at": None,
                    "images": images,
                    "figma_links": figma_links,
                    "doc_type": "online"
                }

        except httpx.HTTPStatusError as e:
            error_msg = f"HTTP错误: {e.response.status_code}"
            if e.response.status_code == 403:
                error_msg = "访问被拒绝，可能是权限问题"
            elif e.response.status_code == 404:
                error_msg = "文档不存在"

            return {
                "success": False,
                "error": error_msg,
                "error_type": "http_error"
            }
        except Exception as e:
            logger.error(f"读取在线文档失败: {url}, 错误: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "error_type": "online_read_error"
            }

    async def _download_file(self, url: str) -> Optional[str]:
        """
        下载文件到临时目录

        Args:
            url: 文件链接

        Returns:
            本地文件路径或None
        """
        try:
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.get(url)
                response.raise_for_status()

                # 生成临时文件名
                parsed_url = urlparse(url)
                filename = os.path.basename(parsed_url.path)
                if not filename or '.' not in filename:
                    filename = f"temp_doc_{hash(url)}.bin"

                temp_path = os.path.join("uploads", "temp", filename)
                os.makedirs(os.path.dirname(temp_path), exist_ok=True)

                # 保存文件
                async with aiofiles.open(temp_path, 'wb') as f:
                    await f.write(response.content)

                return temp_path

        except Exception as e:
            logger.error(f"下载文件失败: {url}, 错误: {str(e)}")
            return None
