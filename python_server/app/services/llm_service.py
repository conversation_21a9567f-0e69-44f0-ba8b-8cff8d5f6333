"""
大模型交互服务（支持SSE流式响应）
"""
import json
import asyncio
from typing import Dict, Any, AsyncGenerator, Optional
from openai import AsyncOpenAI
from anthropic import AsyncAnthropic
from loguru import logger

from config import settings
from app.services.qwen_service import QwenService


class LLMService:
    """大模型服务类（支持流式响应）"""
    
    def __init__(self):
        self.qwen_service = None
        self.openai_client = None
        self.anthropic_client = None

        # 初始化千问服务（优先使用）
        if settings.dashscope_api_key:
            self.qwen_service = QwenService()

        # 初始化OpenAI客户端
        if settings.openai_api_key:
            self.openai_client = AsyncOpenAI(
                api_key=settings.openai_api_key,
                base_url=settings.openai_base_url
            )

        # 初始化Anthropic客户端
        if settings.anthropic_api_key:
            self.anthropic_client = AsyncAnthropic(
                api_key=settings.anthropic_api_key
            )
    
    async def generate_testcases_stream(self, prd_content: str, system_prompt: str = None) -> AsyncGenerator[str, None]:
        """
        流式生成测试用例
        
        Args:
            prd_content: PRD文档内容
            system_prompt: 系统提示词
            
        Yields:
            流式响应数据
        """
        try:
            # 优先使用千问流式API
            if self.qwen_service:
                async for chunk in self.qwen_service.generate_testcases_stream(prd_content, system_prompt):
                    yield chunk
                return

            # 构建完整的提示词
            full_prompt = self._build_complete_prompt(prd_content, system_prompt)

            # 备用：使用OpenAI流式API
            if self.openai_client:
                async for chunk in self._stream_openai(full_prompt):
                    yield chunk
            elif self.anthropic_client:
                async for chunk in self._stream_anthropic(full_prompt):
                    yield chunk
            else:
                yield self._format_sse_data({
                    "type": "error",
                    "message": "没有可用的AI服务"
                })
                
        except Exception as e:
            logger.error(f"流式生成测试用例失败: {str(e)}")
            yield self._format_sse_data({
                "type": "error",
                "message": str(e)
            })
    
    def _build_complete_prompt(self, prd_content: str, system_prompt: str = None) -> str:
        """构建完整的提示词"""
        if system_prompt:
            base_prompt = system_prompt
        else:
            base_prompt = """
你是一个专业的测试工程师，擅长根据产品需求文档(PRD)生成全面的测试用例。

请根据提供的PRD内容，生成详细的测试用例集合。测试用例应该包括：
1. 功能测试用例 - 验证核心功能是否正常工作
2. 边界测试用例 - 验证边界条件和极限情况
3. 异常测试用例 - 验证错误处理和异常情况
4. 用户体验测试用例 - 验证用户交互和体验

每个测试用例应包含：
- 用例标题
- 用例描述
- 优先级（高/中/低）
- 前置条件
- 测试步骤（详细可执行）
- 预期结果
- 测试数据（如需要）

请使用Markdown格式输出，结构清晰，便于阅读和执行。
"""
        
        full_prompt = f"{base_prompt}\n\nPRD文档内容：\n{prd_content[:8000]}"  # 限制长度
        return full_prompt
    
    async def _stream_openai(self, prompt: str) -> AsyncGenerator[str, None]:
        """OpenAI流式响应"""
        try:
            # 发送开始信号
            yield self._format_sse_data({
                "type": "start",
                "message": "开始生成测试用例..."
            })
            
            stream = await self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=4000,
                stream=True
            )
            
            async for chunk in stream:
                if chunk.choices[0].delta.content is not None:
                    content = chunk.choices[0].delta.content
                    yield self._format_sse_data({
                        "type": "content",
                        "content": content
                    })
            
            # 发送完成信号
            yield self._format_sse_data({
                "type": "complete",
                "message": "测试用例生成完成"
            })
            
        except Exception as e:
            logger.error(f"OpenAI流式调用失败: {str(e)}")
            yield self._format_sse_data({
                "type": "error",
                "message": str(e)
            })
    
    async def _stream_anthropic(self, prompt: str) -> AsyncGenerator[str, None]:
        """Anthropic流式响应"""
        try:
            # 发送开始信号
            yield self._format_sse_data({
                "type": "start",
                "message": "开始生成测试用例..."
            })
            
            async with self.anthropic_client.messages.stream(
                model="claude-3-sonnet-20240229",
                max_tokens=4000,
                temperature=0.3,
                messages=[
                    {"role": "user", "content": prompt}
                ]
            ) as stream:
                async for text in stream.text_stream:
                    yield self._format_sse_data({
                        "type": "content",
                        "content": text
                    })
            
            # 发送完成信号
            yield self._format_sse_data({
                "type": "complete",
                "message": "测试用例生成完成"
            })
            
        except Exception as e:
            logger.error(f"Anthropic流式调用失败: {str(e)}")
            yield self._format_sse_data({
                "type": "error",
                "message": str(e)
            })
    
    def _format_sse_data(self, data: Dict[str, Any]) -> str:
        """格式化SSE数据"""
        return f"data: {json.dumps(data, ensure_ascii=False)}\n\n"
    
    async def generate_system_prompt(self, doc_metadata: Dict[str, Any]) -> str:
        """
        根据文档元数据生成系统提示词
        
        Args:
            doc_metadata: 文档元数据
            
        Returns:
            系统提示词
        """
        title = doc_metadata.get("title", "")
        author = doc_metadata.get("author", "")
        doc_type = doc_metadata.get("doc_type", "")
        images_count = len(doc_metadata.get("images", []))
        figma_count = len(doc_metadata.get("figma_links", []))
        
        system_prompt = f"""
你是一个专业的测试工程师，正在为以下文档生成测试用例：

文档信息：
- 标题：{title}
- 作者：{author}
- 文档类型：{doc_type}
- 包含图片：{images_count}个
- 包含Figma链接：{figma_count}个

请根据文档内容的特点，生成针对性的测试用例。特别注意：
1. 如果文档包含UI设计（有Figma链接或图片），请重点关注UI测试用例
2. 如果是功能性PRD，请重点关注功能测试和业务流程测试
3. 如果是技术性文档，请重点关注接口测试和性能测试
4. 考虑用户角色和使用场景的多样性

测试用例应该全面、具体、可执行，并按优先级合理分类。
使用Markdown格式输出，结构清晰。
"""
        
        return system_prompt
    
    async def validate_and_enhance_prompt(self, base_prompt: str, prd_content: str) -> str:
        """
        验证和增强提示词
        
        Args:
            base_prompt: 基础提示词
            prd_content: PRD内容
            
        Returns:
            增强后的提示词
        """
        # 分析PRD内容特征
        content_features = self._analyze_content_features(prd_content)
        
        # 根据内容特征调整提示词
        enhanced_prompt = base_prompt
        
        if content_features["has_api_specs"]:
            enhanced_prompt += "\n\n特别注意：文档包含API规范，请重点生成接口测试用例。"
        
        if content_features["has_user_stories"]:
            enhanced_prompt += "\n\n特别注意：文档包含用户故事，请基于用户场景生成测试用例。"
        
        if content_features["has_business_rules"]:
            enhanced_prompt += "\n\n特别注意：文档包含业务规则，请重点验证业务逻辑的测试用例。"
        
        return enhanced_prompt
    
    def _analyze_content_features(self, content: str) -> Dict[str, bool]:
        """分析内容特征"""
        content_lower = content.lower()
        
        return {
            "has_api_specs": any(keyword in content_lower for keyword in ["api", "接口", "endpoint", "request", "response"]),
            "has_user_stories": any(keyword in content_lower for keyword in ["用户故事", "user story", "作为", "我希望"]),
            "has_business_rules": any(keyword in content_lower for keyword in ["业务规则", "规则", "条件", "如果", "当"])
        }
