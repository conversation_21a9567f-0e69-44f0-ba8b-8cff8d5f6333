"""
Markdown处理服务
"""
import re
import json
import hashlib
from typing import Dict, Any, List, Optional
import markdown
from markdown.extensions import codehilite, tables, toc
from loguru import logger


class MarkdownService:
    """Markdown处理服务类"""
    
    def __init__(self):
        self.md = markdown.Markdown(
            extensions=[
                'codehilite',
                'tables',
                'toc',
                'fenced_code',
                'nl2br'
            ],
            extension_configs={
                'codehilite': {
                    'css_class': 'highlight'
                },
                'toc': {
                    'permalink': True
                }
            }
        )
    
    def validate_markdown(self, content: str) -> Dict[str, Any]:
        """
        验证Markdown文档的合法性
        
        Args:
            content: Markdown内容
            
        Returns:
            验证结果
        """
        try:
            validation_result = {
                "is_valid": True,
                "errors": [],
                "warnings": [],
                "statistics": {}
            }
            
            # 基本语法检查
            syntax_errors = self._check_syntax(content)
            validation_result["errors"].extend(syntax_errors)
            
            # 结构检查
            structure_warnings = self._check_structure(content)
            validation_result["warnings"].extend(structure_warnings)
            
            # 统计信息
            validation_result["statistics"] = self._calculate_statistics(content)
            
            # 如果有错误，标记为无效
            if validation_result["errors"]:
                validation_result["is_valid"] = False
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Markdown验证失败: {str(e)}")
            return {
                "is_valid": False,
                "errors": [f"验证过程出错: {str(e)}"],
                "warnings": [],
                "statistics": {}
            }
    
    def _check_syntax(self, content: str) -> List[str]:
        """检查Markdown语法错误"""
        errors = []
        lines = content.split('\n')
        
        for i, line in enumerate(lines, 1):
            # 检查未闭合的代码块
            if line.strip().startswith('```'):
                # 简单检查：统计代码块标记的数量
                code_block_count = content.count('```')
                if code_block_count % 2 != 0:
                    errors.append(f"第{i}行: 代码块未正确闭合")
            
            # 检查表格语法
            if '|' in line and not line.strip().startswith('|'):
                # 简单的表格语法检查
                if line.count('|') < 2:
                    errors.append(f"第{i}行: 表格语法可能有误")
            
            # 检查链接语法
            link_pattern = r'\[([^\]]*)\]\(([^)]*)\)'
            matches = re.findall(link_pattern, line)
            for match in matches:
                if not match[1]:  # 空链接
                    errors.append(f"第{i}行: 链接地址为空")
        
        return errors
    
    def _check_structure(self, content: str) -> List[str]:
        """检查Markdown结构问题"""
        warnings = []
        
        # 检查标题层级
        headers = re.findall(r'^(#{1,6})\s+(.+)$', content, re.MULTILINE)
        if headers:
            prev_level = 0
            for header in headers:
                current_level = len(header[0])
                if current_level > prev_level + 1:
                    warnings.append(f"标题层级跳跃: 从H{prev_level}直接跳到H{current_level}")
                prev_level = current_level
        
        # 检查是否有主标题
        if not re.search(r'^#\s+', content, re.MULTILINE):
            warnings.append("文档缺少主标题（H1）")
        
        # 检查空行使用
        lines = content.split('\n')
        for i in range(len(lines) - 1):
            if lines[i].strip().startswith('#') and lines[i + 1].strip():
                warnings.append(f"建议在标题后添加空行以提高可读性")
                break
        
        return warnings
    
    def _calculate_statistics(self, content: str) -> Dict[str, Any]:
        """计算Markdown统计信息"""
        stats = {
            "total_lines": len(content.split('\n')),
            "total_chars": len(content),
            "total_words": len(content.split()),
            "headers": {},
            "code_blocks": 0,
            "tables": 0,
            "links": 0,
            "images": 0
        }
        
        # 统计标题
        for level in range(1, 7):
            pattern = f'^{"#" * level}\\s+'
            count = len(re.findall(pattern, content, re.MULTILINE))
            if count > 0:
                stats["headers"][f"h{level}"] = count
        
        # 统计代码块
        stats["code_blocks"] = content.count('```') // 2
        
        # 统计表格
        stats["tables"] = len(re.findall(r'^\|.*\|$', content, re.MULTILINE))
        
        # 统计链接
        stats["links"] = len(re.findall(r'\[([^\]]*)\]\(([^)]*)\)', content))
        
        # 统计图片
        stats["images"] = len(re.findall(r'!\[([^\]]*)\]\(([^)]*)\)', content))
        
        return stats
    
    def convert_to_json(self, content: str) -> Dict[str, Any]:
        """
        将Markdown内容转换为JSON结构
        
        Args:
            content: Markdown内容
            
        Returns:
            转换结果
        """
        try:
            json_structure = {
                "success": True,
                "data": [],
                "metadata": {}
            }
            
            # 解析Markdown结构
            sections = self._parse_sections(content)
            json_structure["data"] = sections
            
            # 添加元数据
            json_structure["metadata"] = {
                "total_sections": len(sections),
                "statistics": self._calculate_statistics(content),
                "generated_at": self._get_current_timestamp()
            }
            
            return json_structure
            
        except Exception as e:
            logger.error(f"Markdown转JSON失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "data": [],
                "metadata": {}
            }
    
    def _parse_sections(self, content: str) -> List[Dict[str, Any]]:
        """解析Markdown章节结构"""
        sections = []
        lines = content.split('\n')
        current_section = None
        current_content = []
        
        for line in lines:
            # 检查是否是标题
            header_match = re.match(r'^(#{1,6})\s+(.+)$', line)
            if header_match:
                # 保存前一个章节
                if current_section:
                    current_section["content"] = '\n'.join(current_content).strip()
                    sections.append(current_section)
                
                # 开始新章节
                level = len(header_match.group(1))
                title = header_match.group(2).strip()
                current_section = {
                    "type": "section",
                    "level": level,
                    "title": title,
                    "content": "",
                    "subsections": []
                }
                current_content = []
            else:
                # 添加到当前章节内容
                if current_section:
                    current_content.append(line)
                else:
                    # 文档开头的内容（没有标题）
                    if not sections:
                        sections.append({
                            "type": "preamble",
                            "level": 0,
                            "title": "前言",
                            "content": "",
                            "subsections": []
                        })
                        current_section = sections[0]
                    current_content.append(line)
        
        # 保存最后一个章节
        if current_section:
            current_section["content"] = '\n'.join(current_content).strip()
            if current_section not in sections:
                sections.append(current_section)
        
        return sections
    
    def calculate_hash(self, content: str) -> str:
        """
        计算Markdown内容的哈希值
        
        Args:
            content: Markdown内容
            
        Returns:
            MD5哈希值
        """
        # 标准化内容（去除多余空白字符）
        normalized_content = re.sub(r'\s+', ' ', content.strip())
        
        # 计算MD5哈希
        return hashlib.md5(normalized_content.encode('utf-8')).hexdigest()
    
    def render_to_html(self, content: str) -> str:
        """
        将Markdown渲染为HTML
        
        Args:
            content: Markdown内容
            
        Returns:
            HTML内容
        """
        try:
            # 重置markdown实例
            self.md.reset()
            
            # 渲染HTML
            html = self.md.convert(content)
            
            return html
            
        except Exception as e:
            logger.error(f"Markdown渲染HTML失败: {str(e)}")
            return f"<p>渲染失败: {str(e)}</p>"
    
    def extract_test_cases(self, content: str) -> List[Dict[str, Any]]:
        """
        从Markdown内容中提取测试用例
        
        Args:
            content: Markdown内容
            
        Returns:
            测试用例列表
        """
        test_cases = []
        
        try:
            # 使用正则表达式匹配测试用例模式
            case_pattern = r'###\s+用例\d*[：:]\s*(.+?)(?=###|\Z)'
            cases = re.findall(case_pattern, content, re.DOTALL)
            
            for i, case_content in enumerate(cases, 1):
                test_case = self._parse_test_case(case_content, i)
                if test_case:
                    test_cases.append(test_case)
            
        except Exception as e:
            logger.error(f"提取测试用例失败: {str(e)}")
        
        return test_cases
    
    def _parse_test_case(self, content: str, case_id: int) -> Optional[Dict[str, Any]]:
        """解析单个测试用例"""
        try:
            test_case = {
                "id": case_id,
                "title": "",
                "description": "",
                "priority": "P2",
                "need_self_test": "否",
                "preconditions": "",
                "steps": [],
                "expected_result": "",
                "test_data": ""
            }
            
            # 提取标题
            title_match = re.search(r'^(.+?)(?:\n|$)', content.strip())
            if title_match:
                test_case["title"] = title_match.group(1).strip()
            
            # 提取各个字段
            fields = {
                "用例描述": "description",
                "优先级": "priority",
                "是否需要自测": "need_self_test",
                "前置条件": "preconditions",
                "预期结果": "expected_result",
                "测试数据": "test_data"
            }
            
            for field_name, field_key in fields.items():
                pattern = f'[*-]\\s*\\*\\*{field_name}\\*\\*[：:]\\s*(.+?)(?=\\n[*-]\\s*\\*\\*|\\Z)'
                match = re.search(pattern, content, re.DOTALL)
                if match:
                    value = match.group(1).strip()
                    if field_key == "priority":
                        # 标准化优先级格式
                        if value.lower() in ["high", "高", "p0"]:
                            value = "P0"
                        elif value.lower() in ["medium", "中", "p1"]:
                            value = "P1"
                        elif value.lower() in ["low", "低", "p2"]:
                            value = "P2"
                    test_case[field_key] = value

            # P0用例自动设置为需要自测
            if test_case["priority"] == "P0" and not test_case.get("need_self_test"):
                test_case["need_self_test"] = "是"
            
            # 提取测试步骤
            steps_pattern = r'[*-]\s*\*\*测试步骤\*\*[：:]\s*(.*?)(?=\n[*-]\s*\*\*|\Z)'
            steps_match = re.search(steps_pattern, content, re.DOTALL)
            if steps_match:
                steps_content = steps_match.group(1).strip()
                steps = re.findall(r'\d+\.\s*(.+?)(?=\n\d+\.|\Z)', steps_content, re.DOTALL)
                test_case["steps"] = [step.strip() for step in steps]
            
            return test_case
            
        except Exception as e:
            logger.error(f"解析测试用例失败: {str(e)}")
            return None
    
    def _get_current_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()
