"""
AI服务模块
"""
import json
from typing import Dict, Any, List, Optional
from openai import AsyncOpenA<PERSON>
from anthropic import AsyncAnthropic
from loguru import logger

from config import settings


class AIService:
    """AI服务类"""
    
    def __init__(self):
        self.openai_client = None
        self.anthropic_client = None
        
        # 初始化OpenAI客户端
        if settings.openai_api_key:
            self.openai_client = AsyncOpenAI(
                api_key=settings.openai_api_key,
                base_url=settings.openai_base_url
            )
        
        # 初始化Anthropic客户端
        if settings.anthropic_api_key:
            self.anthropic_client = AsyncAnthropic(
                api_key=settings.anthropic_api_key
            )
    
    async def summarize_document(self, content: str, doc_type: str = "prd") -> Dict[str, Any]:
        """
        总结文档内容
        
        Args:
            content: 文档内容
            doc_type: 文档类型
            
        Returns:
            总结结果
        """
        try:
            prompt = self._build_summary_prompt(content, doc_type)
            
            # 优先使用OpenAI
            if self.openai_client:
                result = await self._call_openai(prompt, "summary")
            elif self.anthropic_client:
                result = await self._call_anthropic(prompt, "summary")
            else:
                return {
                    "success": False,
                    "error": "没有可用的AI服务"
                }
            
            if result["success"]:
                summary_data = self._parse_summary_response(result["content"])
                return {
                    "success": True,
                    "title": summary_data.get("title", ""),
                    "summary": summary_data.get("summary", ""),
                    "key_points": summary_data.get("key_points", []),
                    "author": summary_data.get("author", ""),
                    "update_time": summary_data.get("update_time", "")
                }
            else:
                return result
                
        except Exception as e:
            logger.error(f"文档总结失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _build_summary_prompt(self, content: str, doc_type: str) -> str:
        """构建总结提示词"""
        base_prompt = f"""
请分析以下{doc_type}文档内容，并提供结构化的总结信息。

文档内容：
{content[:4000]}  # 限制内容长度避免token超限

请按照以下JSON格式返回结果：
{{
    "title": "文档标题",
    "summary": "文档摘要（200字以内）",
    "key_points": ["关键点1", "关键点2", "关键点3"],
    "author": "作者信息（如果能识别）",
    "update_time": "更新时间（如果能识别）"
}}

要求：
1. 标题要简洁明了，体现文档核心内容
2. 摘要要涵盖文档的主要内容和目标
3. 关键点要提取3-5个最重要的信息
4. 如果无法识别作者或时间信息，请返回空字符串
5. 返回的必须是有效的JSON格式
"""
        return base_prompt
    
    async def _call_openai(self, prompt: str, task_type: str) -> Dict[str, Any]:
        """调用OpenAI API"""
        try:
            response = await self.openai_client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "你是一个专业的文档分析助手，擅长提取和总结文档信息。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=1000
            )
            
            content = response.choices[0].message.content
            return {
                "success": True,
                "content": content,
                "model": "openai",
                "task_type": task_type
            }
            
        except Exception as e:
            logger.error(f"OpenAI API调用失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "model": "openai"
            }
    
    async def _call_anthropic(self, prompt: str, task_type: str) -> Dict[str, Any]:
        """调用Anthropic API"""
        try:
            response = await self.anthropic_client.messages.create(
                model="claude-3-sonnet-20240229",
                max_tokens=1000,
                temperature=0.3,
                messages=[
                    {"role": "user", "content": prompt}
                ]
            )
            
            content = response.content[0].text
            return {
                "success": True,
                "content": content,
                "model": "anthropic",
                "task_type": task_type
            }
            
        except Exception as e:
            logger.error(f"Anthropic API调用失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "model": "anthropic"
            }
    
    def _parse_summary_response(self, response: str) -> Dict[str, Any]:
        """解析总结响应"""
        try:
            # 尝试直接解析JSON
            return json.loads(response)
        except json.JSONDecodeError:
            # 如果不是有效JSON，尝试提取JSON部分
            try:
                import re
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    return json.loads(json_match.group())
            except:
                pass
            
            # 如果都失败了，返回默认结构
            return {
                "title": "文档标题提取失败",
                "summary": response[:200] if response else "总结生成失败",
                "key_points": [],
                "author": "",
                "update_time": ""
            }
    
    async def generate_test_cases(self, prd_content: str, requirements: List[str] = None) -> Dict[str, Any]:
        """
        根据PRD内容生成测试用例
        
        Args:
            prd_content: PRD文档内容
            requirements: 特定需求列表
            
        Returns:
            测试用例生成结果
        """
        try:
            prompt = self._build_testcase_prompt(prd_content, requirements)
            
            # 优先使用OpenAI
            if self.openai_client:
                result = await self._call_openai(prompt, "testcase")
            elif self.anthropic_client:
                result = await self._call_anthropic(prompt, "testcase")
            else:
                return {
                    "success": False,
                    "error": "没有可用的AI服务"
                }
            
            if result["success"]:
                return {
                    "success": True,
                    "markdown_content": result["content"],
                    "model": result["model"]
                }
            else:
                return result
                
        except Exception as e:
            logger.error(f"测试用例生成失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _build_testcase_prompt(self, prd_content: str, requirements: List[str] = None) -> str:
        """构建测试用例生成提示词"""
        requirements_text = ""
        if requirements:
            requirements_text = f"\n特别关注以下需求：\n" + "\n".join([f"- {req}" for req in requirements])
        
        prompt = f"""
请根据以下PRD文档内容，生成详细的测试用例。

PRD文档内容：
{prd_content[:6000]}  # 限制内容长度
{requirements_text}

请按照以下Markdown格式生成测试用例：

# 测试用例集

## 功能测试用例

### 用例1：[功能名称]
- **用例标题**：[简洁的标题]
- **用例描述**：[详细描述测试目标]
- **优先级**：高/中/低
- **前置条件**：[测试前需要满足的条件]
- **测试步骤**：
  1. [步骤1]
  2. [步骤2]
  3. [步骤3]
- **预期结果**：[期望的测试结果]
- **测试数据**：[如果需要特定测试数据]

## 边界测试用例

### 用例2：[边界场景]
[按照上述格式继续...]

## 异常测试用例

### 用例3：[异常场景]
[按照上述格式继续...]

要求：
1. 根据PRD内容识别所有主要功能点
2. 为每个功能点生成正常流程、边界条件和异常情况的测试用例
3. 测试步骤要具体可执行
4. 预期结果要明确可验证
5. 优先级要合理分配
6. 至少生成10个测试用例
"""
        return prompt
