"""
飞书服务
"""
import httpx
import json
from typing import Dict, Any, Optional, List
from loguru import logger

from config import settings


class FeishuService:
    """飞书API服务"""
    
    def __init__(self):
        self.app_id = settings.feishu_app_id
        self.app_secret = settings.feishu_app_secret
        self.bot_token = settings.feishu_bot_token
        self.base_url = "https://open.feishu.cn/open-apis"
        self._access_token = None
    
    async def get_access_token(self) -> Optional[str]:
        """获取访问令牌"""
        if self._access_token:
            return self._access_token
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/auth/v3/tenant_access_token/internal",
                    json={
                        "app_id": self.app_id,
                        "app_secret": self.app_secret
                    }
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("code") == 0:
                        self._access_token = data["tenant_access_token"]
                        return self._access_token
                    else:
                        logger.error(f"获取飞书访问令牌失败: {data}")
                        return None
                else:
                    logger.error(f"获取飞书访问令牌HTTP错误: {response.status_code}")
                    return None
                    
        except Exception as e:
            logger.error(f"获取飞书访问令牌异常: {str(e)}")
            return None
    
    async def get_document_content(self, doc_id: str) -> Dict[str, Any]:
        """
        获取飞书文档内容
        
        Args:
            doc_id: 文档ID
            
        Returns:
            文档内容和元数据
        """
        access_token = await self.get_access_token()
        if not access_token:
            return {
                "success": False,
                "error": "无法获取飞书访问令牌",
                "error_type": "auth_error"
            }
        
        try:
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }
            
            async with httpx.AsyncClient() as client:
                # 获取文档元数据
                meta_response = await client.get(
                    f"{self.base_url}/docx/v1/documents/{doc_id}",
                    headers=headers
                )
                
                if meta_response.status_code != 200:
                    return {
                        "success": False,
                        "error": f"获取文档元数据失败: {meta_response.status_code}",
                        "error_type": "api_error"
                    }
                
                meta_data = meta_response.json()
                if meta_data.get("code") != 0:
                    return {
                        "success": False,
                        "error": f"飞书API错误: {meta_data.get('msg', '未知错误')}",
                        "error_type": "feishu_api_error"
                    }
                
                # 获取文档内容
                content_response = await client.get(
                    f"{self.base_url}/docx/v1/documents/{doc_id}/raw_content",
                    headers=headers
                )
                
                if content_response.status_code != 200:
                    return {
                        "success": False,
                        "error": f"获取文档内容失败: {content_response.status_code}",
                        "error_type": "api_error"
                    }
                
                content_data = content_response.json()
                if content_data.get("code") != 0:
                    return {
                        "success": False,
                        "error": f"飞书API错误: {content_data.get('msg', '未知错误')}",
                        "error_type": "feishu_api_error"
                    }
                
                # 解析文档信息
                document = meta_data.get("data", {}).get("document", {})
                content = content_data.get("data", {}).get("content", "")
                
                # 提取图片和Figma链接
                images = self._extract_images_from_content(content)
                figma_links = self._extract_figma_links_from_content(content)
                
                return {
                    "success": True,
                    "content": content,
                    "title": document.get("title", ""),
                    "author": document.get("owner_id", ""),
                    "created_at": document.get("create_time"),
                    "updated_at": document.get("update_time"),
                    "images": images,
                    "figma_links": figma_links
                }
                
        except Exception as e:
            logger.error(f"获取飞书文档内容异常: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "error_type": "exception"
            }
    
    def _extract_images_from_content(self, content: str) -> List[Dict[str, str]]:
        """从文档内容中提取图片信息"""
        images = []
        # 这里需要根据飞书文档的实际格式来解析图片
        # 飞书文档通常以JSON格式返回，包含图片的token等信息
        try:
            # 简单的图片提取逻辑，实际需要根据飞书API文档调整
            import re
            img_matches = re.findall(r'"image"[^}]*"token":\s*"([^"]+)"', content)
            for token in img_matches:
                images.append({
                    "token": token,
                    "url": f"{self.base_url}/drive/v1/medias/{token}/download",
                    "alt": ""
                })
        except Exception as e:
            logger.warning(f"提取图片信息失败: {str(e)}")
        
        return images
    
    def _extract_figma_links_from_content(self, content: str) -> List[Dict[str, str]]:
        """从文档内容中提取Figma链接"""
        figma_links = []
        try:
            import re
            figma_matches = re.findall(r'https://[^"\s]*figma\.com[^"\s]*', content)
            for link in figma_matches:
                figma_links.append({"url": link})
        except Exception as e:
            logger.warning(f"提取Figma链接失败: {str(e)}")
        
        return figma_links
    
    async def send_notification(self, user_id: str, message: str, title: str = "AI自动生成测试用例-已完成") -> Dict[str, Any]:
        """
        发送飞书通知
        
        Args:
            user_id: 用户ID
            message: 消息内容
            title: 消息标题
            
        Returns:
            发送结果
        """
        try:
            headers = {
                "Authorization": f"Bearer {self.bot_token}",
                "Content-Type": "application/json"
            }
            
            # 构建消息内容
            card_content = {
                "config": {
                    "wide_screen_mode": True
                },
                "elements": [
                    {
                        "tag": "div",
                        "text": {
                            "content": message,
                            "tag": "lark_md"
                        }
                    },
                    {
                        "tag": "action",
                        "actions": [
                            {
                                "tag": "button",
                                "text": {
                                    "content": "前往查看",
                                    "tag": "plain_text"
                                },
                                "type": "primary",
                                "url": "https://your-frontend-url.com/testcases"
                            }
                        ]
                    }
                ],
                "header": {
                    "title": {
                        "content": title,
                        "tag": "plain_text"
                    }
                }
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/im/v1/messages",
                    headers=headers,
                    json={
                        "receive_id": user_id,
                        "receive_id_type": "user_id",
                        "msg_type": "interactive",
                        "content": json.dumps(card_content)
                    }
                )
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("code") == 0:
                        return {"success": True, "message_id": data.get("data", {}).get("message_id")}
                    else:
                        return {"success": False, "error": data.get("msg", "发送失败")}
                else:
                    return {"success": False, "error": f"HTTP错误: {response.status_code}"}
                    
        except Exception as e:
            logger.error(f"发送飞书通知异常: {str(e)}")
            return {"success": False, "error": str(e)}
