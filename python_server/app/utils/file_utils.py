"""
文件处理工具
"""
import os
import hashlib
from typing import Dict, Any, Optional
from docx import Document
from PyPDF2 import PdfReader
from openpyxl import load_workbook
from pptx import Presentation
from loguru import logger


class FileUtils:
    """文件处理工具类"""
    
    async def extract_content(self, file_path: str) -> Dict[str, Any]:
        """
        从文件中提取内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            提取结果
        """
        try:
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext == '.pdf':
                return await self._extract_pdf_content(file_path)
            elif file_ext in ['.doc', '.docx']:
                return await self._extract_word_content(file_path)
            elif file_ext in ['.xls', '.xlsx']:
                return await self._extract_excel_content(file_path)
            elif file_ext in ['.ppt', '.pptx']:
                return await self._extract_ppt_content(file_path)
            else:
                return {
                    "success": False,
                    "error": f"不支持的文件类型: {file_ext}"
                }
                
        except Exception as e:
            logger.error(f"提取文件内容失败: {file_path}, 错误: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _extract_pdf_content(self, file_path: str) -> Dict[str, Any]:
        """提取PDF内容"""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PdfReader(file)
                content = ""
                
                for page in pdf_reader.pages:
                    content += page.extract_text() + "\n"
                
                # 提取元数据
                metadata = pdf_reader.metadata
                title = metadata.get('/Title', '') if metadata else ''
                author = metadata.get('/Author', '') if metadata else ''
                
                return {
                    "success": True,
                    "content": content.strip(),
                    "title": title,
                    "author": author,
                    "images": []  # PDF图片提取需要更复杂的处理
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"PDF解析失败: {str(e)}"
            }
    
    async def _extract_word_content(self, file_path: str) -> Dict[str, Any]:
        """提取Word文档内容"""
        try:
            doc = Document(file_path)
            content = ""
            
            # 提取段落内容
            for paragraph in doc.paragraphs:
                content += paragraph.text + "\n"
            
            # 提取表格内容
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        row_text.append(cell.text.strip())
                    content += " | ".join(row_text) + "\n"
            
            # 提取文档属性
            core_props = doc.core_properties
            title = core_props.title or ""
            author = core_props.author or ""
            created = core_props.created
            modified = core_props.modified
            
            # 提取图片信息
            images = []
            for rel in doc.part.rels.values():
                if "image" in rel.target_ref:
                    images.append({
                        "name": os.path.basename(rel.target_ref),
                        "type": "embedded"
                    })
            
            return {
                "success": True,
                "content": content.strip(),
                "title": title,
                "author": author,
                "created_at": created,
                "updated_at": modified,
                "images": images
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Word文档解析失败: {str(e)}"
            }
    
    async def _extract_excel_content(self, file_path: str) -> Dict[str, Any]:
        """提取Excel内容"""
        try:
            workbook = load_workbook(file_path, read_only=True)
            content = ""
            
            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                content += f"工作表: {sheet_name}\n"
                
                for row in sheet.iter_rows(values_only=True):
                    row_text = []
                    for cell in row:
                        if cell is not None:
                            row_text.append(str(cell))
                        else:
                            row_text.append("")
                    content += " | ".join(row_text) + "\n"
                content += "\n"
            
            # 提取文档属性
            props = workbook.properties
            title = props.title or ""
            author = props.creator or ""
            created = props.created
            modified = props.modified
            
            return {
                "success": True,
                "content": content.strip(),
                "title": title,
                "author": author,
                "created_at": created,
                "updated_at": modified,
                "images": []
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Excel文档解析失败: {str(e)}"
            }
    
    async def _extract_ppt_content(self, file_path: str) -> Dict[str, Any]:
        """提取PowerPoint内容"""
        try:
            prs = Presentation(file_path)
            content = ""
            
            for i, slide in enumerate(prs.slides, 1):
                content += f"幻灯片 {i}:\n"
                
                for shape in slide.shapes:
                    if hasattr(shape, "text"):
                        content += shape.text + "\n"
                
                content += "\n"
            
            # 提取文档属性
            core_props = prs.core_properties
            title = core_props.title or ""
            author = core_props.author or ""
            created = core_props.created
            modified = core_props.modified
            
            return {
                "success": True,
                "content": content.strip(),
                "title": title,
                "author": author,
                "created_at": created,
                "updated_at": modified,
                "images": []
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"PowerPoint文档解析失败: {str(e)}"
            }
    
    @staticmethod
    def calculate_hash(content: str) -> str:
        """计算内容的MD5哈希值"""
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    @staticmethod
    def validate_file_size(file_path: str, max_size_mb: int = 50) -> bool:
        """验证文件大小"""
        try:
            file_size = os.path.getsize(file_path)
            max_size_bytes = max_size_mb * 1024 * 1024
            return file_size <= max_size_bytes
        except:
            return False
