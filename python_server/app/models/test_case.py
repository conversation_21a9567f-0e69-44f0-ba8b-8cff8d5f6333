"""
测试用例数据模型
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, JSON, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.core.database import Base


class TestCase(Base):
    """测试用例表"""
    __tablename__ = "test_cases"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 关联PRD文档
    prd_document_id = Column(Integer, ForeignKey("prd_documents.id"), nullable=False, index=True)
    prd_document = relationship("PRDDocument", backref="test_cases")
    
    # 测试用例基本信息
    case_module = Column(String(200), nullable=False, comment="测试用例模块")
    case_title = Column(String(200), nullable=False, comment="测试用例标题")
    case_description = Column(Text, nullable=True, comment="测试用例描述")
    case_type = Column(String(50), nullable=True, comment="用例类型：功能测试、性能测试等")
    priority = Column(String(20), default="medium", comment="优先级：high, medium, low")
    
    # 测试步骤和预期结果
    test_steps = Column(JSON, nullable=True, comment="测试步骤列表")
    expected_result = Column(Text, nullable=True, comment="预期结果")
    
    # 测试数据
    test_data = Column(JSON, nullable=True, comment="测试数据")
    
    # 前置条件和后置条件
    preconditions = Column(Text, nullable=True, comment="前置条件")
    postconditions = Column(Text, nullable=True, comment="后置条件")
    
    # 系统字段
    created_at = Column(DateTime, server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 状态字段
    status = Column(String(20), default="draft", comment="状态：draft, reviewed, approved")
    
    def __repr__(self):
        return f"<TestCase(id={self.id}, title='{self.case_title}', prd_id={self.prd_document_id})>"
