"""
PRD文档数据模型
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, JSON
from sqlalchemy.sql import func
from app.core.database import Base


class PRDDocument(Base):
    """PRD文档表"""
    __tablename__ = "prd_documents"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # 文档基本信息
    url = Column(String(500), nullable=False, index=True, comment="文档链接")
    title = Column(String(200), nullable=True, comment="文档标题")
    content = Column(Text, nullable=True, comment="文档原始内容")
    
    # 文档元数据
    author = Column(String(100), nullable=True, comment="文档作者")
    doc_created_at = Column(DateTime, nullable=True, comment="文档创建时间")
    doc_updated_at = Column(DateTime, nullable=True, comment="文档修改时间")
    
    # AI生成内容
    markdown_content = Column(Text, nullable=True, comment="生成的markdown内容")
    json_content = Column(JSON, nullable=True, comment="转换为json的列表")
    content_hash = Column(String(64), nullable=True, index=True, comment="markdown内容的hash值")
    prompt = Column(Text, nullable=True, comment="使用的提示词")
    
    # 系统字段
    created_at = Column(DateTime, server_default=func.now(), comment="记录创建时间")
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), comment="记录更新时间")
    
    # 状态字段
    status = Column(String(20), default="pending", comment="处理状态: pending, processing, completed, failed")
    error_message = Column(Text, nullable=True, comment="错误信息")
    
    # 图片和链接信息
    images = Column(JSON, nullable=True, comment="文档中的图片信息")
    figma_links = Column(JSON, nullable=True, comment="Figma链接信息")
    
    def __repr__(self):
        return f"<PRDDocument(id={self.id}, url='{self.url}', title='{self.title}')>"
