#!/usr/bin/env python3
"""
数据库初始化脚本
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.database import init_db, engine
from app.models import prd_document, test_case
from config import settings
from loguru import logger


def run_migrations():
    """运行数据库迁移脚本"""
    migrations_dir = "migrations"
    if not os.path.exists(migrations_dir):
        logger.info("没有找到迁移文件目录")
        return
    
    migration_files = sorted([f for f in os.listdir(migrations_dir) if f.endswith('.sql')])
    
    if not migration_files:
        logger.info("没有找到迁移文件")
        return
    
    logger.info(f"找到 {len(migration_files)} 个迁移文件")
    
    for migration_file in migration_files:
        migration_path = os.path.join(migrations_dir, migration_file)
        logger.info(f"执行迁移: {migration_file}")
        
        try:
            with open(migration_path, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            # 分割SQL语句（以分号分隔）
            sql_statements = [stmt.strip() for stmt in sql_content.split(';') if stmt.strip()]
            
            with engine.connect() as conn:
                for sql in sql_statements:
                    if sql.strip():
                        try:
                            conn.execute(sql)
                            conn.commit()
                        except Exception as e:
                            logger.warning(f"SQL语句执行警告: {sql[:50]}... - {str(e)}")
            
            logger.info(f"迁移 {migration_file} 执行完成")
            
        except Exception as e:
            logger.error(f"迁移 {migration_file} 执行失败: {str(e)}")


async def main():
    """主函数"""
    logger.info("开始初始化数据库...")
    logger.info(f"数据库连接: {settings.get_database_url}")
    
    try:
        # 初始化数据库表
        logger.info("创建数据库表...")
        await init_db()
        logger.info("数据库表创建完成")
        
        # 运行迁移脚本
        logger.info("运行数据库迁移...")
        run_migrations()
        logger.info("数据库迁移完成")
        
        logger.info("数据库初始化成功！")
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
