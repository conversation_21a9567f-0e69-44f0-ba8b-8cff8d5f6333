-- 更新测试用例表结构
-- 添加新字段：优先级和是否需要自测

-- 1. 修改优先级字段
ALTER TABLE test_cases MODIFY COLUMN priority VARCHAR(10) NOT NULL DEFAULT 'P2' COMMENT '优先级：P0、P1、P2等';

-- 2. 添加是否需要自测字段
ALTER TABLE test_cases ADD COLUMN need_self_test VARCHAR(10) NOT NULL DEFAULT '否' COMMENT '是否需要自测：是/否（P0必须为是）';

-- 3. 更新现有数据的优先级格式
UPDATE test_cases SET priority = 'P0' WHERE priority IN ('high', '高', 'High', 'HIGH');
UPDATE test_cases SET priority = 'P1' WHERE priority IN ('medium', '中', 'Medium', 'MEDIUM');
UPDATE test_cases SET priority = 'P2' WHERE priority IN ('low', '低', 'Low', 'LOW');

-- 4. 为P0用例自动设置需要自测
UPDATE test_cases SET need_self_test = '是' WHERE priority = 'P0';

-- 5. 添加case_module字段（如果不存在）
ALTER TABLE test_cases ADD COLUMN IF NOT EXISTS case_module VARCHAR(200) NOT NULL DEFAULT '' COMMENT '测试用例模块';

-- 6. 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_test_cases_priority ON test_cases(priority);
CREATE INDEX IF NOT EXISTS idx_test_cases_need_self_test ON test_cases(need_self_test);
CREATE INDEX IF NOT EXISTS idx_test_cases_module ON test_cases(case_module);
